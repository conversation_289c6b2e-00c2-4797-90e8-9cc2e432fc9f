from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func
from typing import List, Optional, Dict
from ..database import get_async_db
from ..models import SystemSettings, EmailTemplate, EmailLog, User
from ..schemas import (
    SystemSettingsCreate, SystemSettingsUpdate, SystemSettingsResponse,
    EmailTemplateCreate, EmailTemplateUpdate, EmailTemplateResponse,
    EmailLogResponse, EmailSendRequest
)
from ..auth import get_current_user
from ..email_service import email_service
from ..email_hooks import email_hooks

router = APIRouter(prefix="/api/settings", tags=["系统设置"])


async def check_admin_permission(current_user: User = Depends(get_current_user)):
    """检查管理员权限"""
    if current_user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要管理员权限"
        )
    return current_user


# 系统设置相关
@router.get("/system", response_model=List[SystemSettingsResponse])
async def get_system_settings(
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(check_admin_permission)
):
    """获取所有系统设置"""
    stmt = select(SystemSettings).order_by(SystemSettings.key)
    result = await db.execute(stmt)
    settings = result.scalars().all()
    return settings


@router.put("/system/batch")
async def batch_update_settings(
    settings: Dict[str, str],
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(check_admin_permission)
):
    """批量更新系统设置"""
    print(f"Received settings: {settings}")  # 调试日志
    updated_count = 0

    for key, value in settings.items():
        print(f"Processing: {key} = {value} (type: {type(value)})")  # 调试日志
        stmt = select(SystemSettings).where(SystemSettings.key == key)
        result = await db.execute(stmt)
        setting = result.scalar_one_or_none()

        if setting:
            # 更新现有设置
            setting.value = str(value)  # 确保转换为字符串
            updated_count += 1
        else:
            # 创建新设置
            new_setting = SystemSettings(
                key=key,
                value=str(value),  # 确保转换为字符串
                description=f"自动创建的设置项: {key}"
            )
            db.add(new_setting)
            updated_count += 1

    await db.commit()
    return {"message": f"已更新 {updated_count} 个设置项"}


@router.get("/system/{key}", response_model=SystemSettingsResponse)
async def get_system_setting(
    key: str,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(check_admin_permission)
):
    """获取指定的系统设置"""
    stmt = select(SystemSettings).where(SystemSettings.key == key)
    result = await db.execute(stmt)
    setting = result.scalar_one_or_none()
    if not setting:
        raise HTTPException(status_code=404, detail="设置项不存在")
    return setting


@router.post("/system", response_model=SystemSettingsResponse)
async def create_system_setting(
    setting: SystemSettingsCreate,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(check_admin_permission)
):
    """创建系统设置"""
    # 检查key是否已存在
    stmt = select(SystemSettings).where(SystemSettings.key == setting.key)
    result = await db.execute(stmt)
    existing = result.scalar_one_or_none()
    if existing:
        raise HTTPException(status_code=400, detail="设置项已存在")

    db_setting = SystemSettings(**setting.model_dump())
    db.add(db_setting)
    await db.commit()
    await db.refresh(db_setting)
    return db_setting


@router.put("/system/{key}", response_model=SystemSettingsResponse)
async def update_system_setting(
    key: str,
    setting_update: SystemSettingsUpdate,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(check_admin_permission)
):
    """更新系统设置"""
    stmt = select(SystemSettings).where(SystemSettings.key == key)
    result = await db.execute(stmt)
    setting = result.scalar_one_or_none()
    if not setting:
        raise HTTPException(status_code=404, detail="设置项不存在")

    for field, value in setting_update.model_dump(exclude_unset=True).items():
        setattr(setting, field, value)

    await db.commit()
    await db.refresh(setting)
    return setting


@router.delete("/system/{key}")
async def delete_system_setting(
    key: str,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(check_admin_permission)
):
    """删除系统设置"""
    stmt = select(SystemSettings).where(SystemSettings.key == key)
    result = await db.execute(stmt)
    setting = result.scalar_one_or_none()
    if not setting:
        raise HTTPException(status_code=404, detail="设置项不存在")

    await db.delete(setting)
    await db.commit()
    return {"message": "设置项已删除"}


# 获取注册开关状态（公开接口）
@router.get("/registration-enabled")
async def get_registration_status(db: AsyncSession = Depends(get_async_db)):
    """获取注册开关状态（公开接口）"""
    stmt = select(SystemSettings).where(SystemSettings.key == "registration_enabled")
    result = await db.execute(stmt)
    setting = result.scalar_one_or_none()
    if not setting:
        # 默认不允许注册
        return {"enabled": False}
    return {"enabled": setting.value.lower() == "true"}


# 获取首页显示设置（公开接口）
@router.get("/homepage-enabled")
async def get_homepage_status(db: AsyncSession = Depends(get_async_db)):
    """获取首页显示设置（公开接口）"""
    stmt = select(SystemSettings).where(SystemSettings.key == "show_homepage")
    result = await db.execute(stmt)
    setting = result.scalar_one_or_none()
    if not setting:
        # 默认不显示首页
        return {"enabled": False}
    return {"enabled": setting.value.lower() == "true"}


# 邮件模板相关
@router.get("/email-templates", response_model=List[EmailTemplateResponse])
async def get_email_templates(
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(check_admin_permission)
):
    """获取所有邮件模板"""
    stmt = select(EmailTemplate).order_by(EmailTemplate.created_at.desc())
    result = await db.execute(stmt)
    templates = result.scalars().all()
    return templates


@router.get("/email-templates/{template_id}", response_model=EmailTemplateResponse)
async def get_email_template(
    template_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(check_admin_permission)
):
    """获取指定邮件模板"""
    stmt = select(EmailTemplate).where(EmailTemplate.id == template_id)
    result = await db.execute(stmt)
    template = result.scalar_one_or_none()
    if not template:
        raise HTTPException(status_code=404, detail="邮件模板不存在")
    return template


@router.post("/email-templates", response_model=EmailTemplateResponse)
async def create_email_template(
    template: EmailTemplateCreate,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(check_admin_permission)
):
    """创建邮件模板"""
    # 检查模板名称是否已存在
    stmt = select(EmailTemplate).where(EmailTemplate.name == template.name)
    result = await db.execute(stmt)
    existing = result.scalar_one_or_none()
    if existing:
        raise HTTPException(status_code=400, detail="模板名称已存在")

    db_template = EmailTemplate(**template.model_dump())
    db.add(db_template)
    await db.commit()
    await db.refresh(db_template)
    return db_template


@router.put("/email-templates/{template_id}", response_model=EmailTemplateResponse)
async def update_email_template(
    template_id: int,
    template_update: EmailTemplateUpdate,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(check_admin_permission)
):
    """更新邮件模板"""
    stmt = select(EmailTemplate).where(EmailTemplate.id == template_id)
    result = await db.execute(stmt)
    template = result.scalar_one_or_none()
    if not template:
        raise HTTPException(status_code=404, detail="邮件模板不存在")

    for field, value in template_update.model_dump(exclude_unset=True).items():
        setattr(template, field, value)

    await db.commit()
    await db.refresh(template)
    return template


@router.delete("/email-templates/{template_id}")
async def delete_email_template(
    template_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(check_admin_permission)
):
    """删除邮件模板"""
    stmt = select(EmailTemplate).where(EmailTemplate.id == template_id)
    result = await db.execute(stmt)
    template = result.scalar_one_or_none()
    if not template:
        raise HTTPException(status_code=404, detail="邮件模板不存在")

    await db.delete(template)
    await db.commit()
    return {"message": "邮件模板已删除"}


# 邮件日志相关
@router.get("/email-logs", response_model=List[EmailLogResponse])
async def get_email_logs(
    skip: int = 0,
    limit: int = 100,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(check_admin_permission)
):
    """获取邮件发送日志"""
    stmt = select(EmailLog).order_by(EmailLog.created_at.desc()).offset(skip).limit(limit)
    result = await db.execute(stmt)
    logs = result.scalars().all()
    return logs


# 发送测试邮件
@router.post("/send-test-email")
async def send_test_email(
    email_request: EmailSendRequest,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(check_admin_permission)
):
    """发送测试邮件"""
    success = await email_service.send_email(
        to_email=email_request.to_email,
        subject=email_request.subject,
        content=email_request.content,
        template_name=email_request.template_name,
        db=db
    )

    if success:
        return {"message": "测试邮件发送成功"}
    else:
        raise HTTPException(status_code=500, detail="邮件发送失败")


# 邮件事件映射管理
@router.get("/email-events")
async def get_email_events(
    current_user: User = Depends(check_admin_permission)
):
    """获取所有邮件事件映射"""
    return {
        "events": email_hooks.event_template_mapping,
        "available_events": [
            {"key": "user_registered", "name": "用户注册", "description": "用户成功注册时触发"},
            {"key": "task_created", "name": "任务创建", "description": "创建新任务时触发"},
            {"key": "task_completed", "name": "任务完成", "description": "任务状态变为已完成时触发"},
            {"key": "task_cancelled", "name": "任务取消", "description": "任务状态变为已取消时触发"},
            {"key": "account_submitted", "name": "账号提交", "description": "提交账号信息时触发"},
            {"key": "email_verification", "name": "邮箱确认", "description": "需要验证邮箱真实性时触发"},
            {"key": "password_reset_requested", "name": "密码重置", "description": "请求密码重置时触发"},
        ]
    }


@router.post("/email-events/{event_type}")
async def set_email_event_template(
    event_type: str,
    template_name: str,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(check_admin_permission)
):
    """设置邮件事件对应的模板"""
    # 检查模板是否存在
    stmt = select(EmailTemplate).where(EmailTemplate.name == template_name)
    result = await db.execute(stmt)
    template = result.scalar_one_or_none()
    if not template:
        raise HTTPException(status_code=404, detail="邮件模板不存在")

    # 设置映射
    email_hooks.add_event_template_mapping(event_type, template_name)

    return {"message": f"已设置事件 {event_type} 使用模板 {template_name}"}


@router.delete("/email-events/{event_type}")
async def remove_email_event_template(
    event_type: str,
    current_user: User = Depends(check_admin_permission)
):
    """移除邮件事件映射"""
    email_hooks.remove_event_template_mapping(event_type)
    return {"message": f"已移除事件 {event_type} 的邮件映射"}
