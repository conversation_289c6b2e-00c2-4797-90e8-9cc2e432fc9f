from datetime import timed<PERSON><PERSON>
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OAuth2P<PERSON><PERSON><PERSON><PERSON><PERSON>F<PERSON>
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from ..database import get_async_db
from ..models import User, SystemSettings
from ..schemas import UserCreate, UserResponse, Token
from ..auth import verify_password, get_password_hash, create_access_token, get_current_active_user
from ..config import settings
from ..email_service import email_service
from ..email_hooks import email_hooks
from ..email_verification import email_verification_service

router = APIRouter(prefix="/api/auth", tags=["认证"])


@router.post("/register", response_model=UserResponse)
async def register(user: UserCreate, db: AsyncSession = Depends(get_async_db)):
    """用户注册"""
    # 检查注册开关
    stmt = select(SystemSettings).where(SystemSettings.key == "registration_enabled")
    result = await db.execute(stmt)
    registration_setting = result.scalar_one_or_none()

    if registration_setting and registration_setting.value.lower() != "true":
        raise HTTPException(
            status_code=403,
            detail="Registration is currently disabled"
        )

    # 检查用户名是否已存在
    stmt = select(User).where(User.username == user.username)
    result = await db.execute(stmt)
    db_user = result.scalar_one_or_none()
    if db_user:
        raise HTTPException(
            status_code=400,
            detail="Username already registered"
        )

    # 检查邮箱是否已存在
    stmt = select(User).where(User.email == user.email)
    result = await db.execute(stmt)
    db_user = result.scalar_one_or_none()
    if db_user:
        raise HTTPException(
            status_code=400,
            detail="Email already registered"
        )

    # 创建新用户
    hashed_password = get_password_hash(user.password)
    db_user = User(
        username=user.username,
        email=user.email,
        password_hash=hashed_password
    )
    db.add(db_user)
    await db.commit()
    await db.refresh(db_user)

    # 发送注册欢迎邮件和邮箱验证邮件
    try:
        # 发送欢迎邮件
        await email_hooks.trigger("user_registered", db=db, user=db_user)

        # 发送邮箱验证邮件
        await email_verification_service.send_registration_verification(db_user, db)

    except Exception as e:
        # 邮件发送失败不影响注册流程
        print(f"发送注册邮件失败: {str(e)}")

    return db_user


@router.post("/send-verification-email")
async def send_verification_email(
    email: str,
    db: AsyncSession = Depends(get_async_db)
):
    """发送邮箱验证邮件"""
    # 查找用户
    stmt = select(User).where(User.email == email)
    result = await db.execute(stmt)
    user = result.scalar_one_or_none()
    if not user:
        raise HTTPException(
            status_code=404,
            detail="用户不存在"
        )

    # 发送验证邮件
    success = await email_verification_service.send_registration_verification(user, db)

    if success:
        return {"message": "验证邮件发送成功"}
    else:
        raise HTTPException(status_code=500, detail="验证邮件发送失败")


@router.post("/verify-email")
async def verify_email(
    token: str,
    db: AsyncSession = Depends(get_async_db)
):
    """验证邮箱"""
    # 验证令牌
    token_info = email_verification_service.consume_token(token)
    if not token_info:
        raise HTTPException(
            status_code=400,
            detail="验证令牌无效或已过期"
        )

    # 查找用户
    stmt = select(User).where(User.id == token_info["user_id"])
    result = await db.execute(stmt)
    user = result.scalar_one_or_none()
    if not user:
        raise HTTPException(
            status_code=404,
            detail="用户不存在"
        )

    # 更新用户的邮箱验证状态
    from datetime import datetime, timezone, timedelta
    CHINA_TZ = timezone(timedelta(hours=8))

    user.email_verified = True
    user.email_verified_at = datetime.now(CHINA_TZ)
    await db.commit()
    await db.refresh(user)

    return {
        "message": "邮箱验证成功",
        "user_id": user.id,
        "email": token_info["email"],
        "verified_at": user.email_verified_at
    }


@router.post("/login", response_model=Token)
async def login(form_data: OAuth2PasswordRequestForm = Depends(), db: AsyncSession = Depends(get_async_db)):
    """用户登录"""
    stmt = select(User).where(User.username == form_data.username)
    result = await db.execute(stmt)
    user = result.scalar_one_or_none()
    if not user or not verify_password(form_data.password, user.password_hash):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )

    access_token_expires = timedelta(minutes=settings.access_token_expire_minutes)
    access_token = create_access_token(
        data={"sub": user.username}, expires_delta=access_token_expires
    )
    return {"access_token": access_token, "token_type": "bearer"}


@router.get("/me", response_model=UserResponse)
async def get_current_user_info(current_user: User = Depends(get_current_active_user)):
    """获取当前用户信息"""
    return current_user
