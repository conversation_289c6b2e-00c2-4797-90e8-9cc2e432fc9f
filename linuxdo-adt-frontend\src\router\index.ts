import { createRouter, createWebHistory } from 'vue-router'
import DynamicHome from '../views/DynamicHome.vue'
import Login from '../views/Login.vue'
import Dashboard from '../views/Dashboard.vue'
import TaskList from '../views/TaskList.vue'
import AccountList from '../views/AccountList.vue'
import NoticeList from '../views/NoticeList.vue'
import UserManagement from '../views/UserManagement.vue'
import Profile from '../views/Profile.vue'
import EmailVerification from '../views/EmailVerification.vue'
import SharePage from '../views/SharePage.vue'
import SystemSettings from '../views/SystemSettings.vue'
import HitokotoList from '../views/HitokotoList.vue'
import api from '../api/index'

const routes = [
  {
    path: '/',
    name: 'Home',
    component: DynamicHome
  },
  {
    path: '/admin/login',
    name: 'Login',
    component: Login
  },
  {
    path: '/admin',
    name: 'Dashboard',
    component: Dashboard,
    meta: { requiresAuth: true },
    children: [
      {
        path: '',
        redirect: '/admin/tasks'
      },
      {
        path: '/admin/tasks',
        name: 'TaskList',
        component: TaskList
      },
      {
        path: '/admin/accounts',
        name: 'AccountList',
        component: AccountList
      },
      {
        path: '/admin/notices',
        name: 'NoticeList',
        component: NoticeList,
        meta: { requiresAuth: true }
      },
      {
        path: '/admin/users',
        name: 'UserManagement',
        component: UserManagement,
        meta: { requiresAdmin: true }
      },
      {
        path: '/admin/profile',
        name: 'Profile',
        component: Profile,
        meta: { requiresAuth: true }
      },
      {
        path: '/admin/settings',
        name: 'SystemSettings',
        component: SystemSettings,
        meta: { requiresAdmin: true }
      },
      {
        path: '/admin/hitokoto',
        name: 'HitokotoList',
        component: HitokotoList,
        meta: { requiresAdmin: true }
      }
    ]
  },
  {
    path: '/share/:shareToken',
    name: 'SharePage',
    component: SharePage
  },
  {
    path: '/verify-email',
    name: 'EmailVerification',
    component: EmailVerification
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    redirect: '/'
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  const token = localStorage.getItem('access_token')

  // 检查是否需要登录
  if ((to.meta.requiresAuth || to.meta.requiresAdmin) && !token) {
    next('/admin/login')
    return
  }

  // 如果已登录访问登录页，重定向到主页
  if (to.path === '/admin/login' && token) {
    next('/admin')
    return
  }

  // 检查是否需要管理员权限
  if (to.meta.requiresAdmin && token) {
    try {
      // 获取用户信息检查权限
      const response = await api.get('/api/auth/me')
      const user = response.data
      if (user.role !== 'admin') {
        // 非管理员访问管理员页面，重定向到主页
        next('/admin')
        return
      }
    } catch (error) {
      console.error('权限检查失败:', error)
      next('/admin/login')
      return
    }
  }

  next()
})

export default router
