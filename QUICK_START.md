# LinuxDo ADT 快速开始指南

## 🚀 一键部署（推荐）

### 前提条件
- 安装 Docker 和 Docker Compose
- 确保端口 3000 和 8000 未被占用

### 部署步骤

1. **克隆项目**
```bash
git clone <your-repo-url>
cd linuxdo-adt
```

2. **配置环境变量**
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑配置文件（重要：修改 SECRET_KEY）
nano .env
```

3. **一键部署**
```bash
# 使用部署脚本
./scripts/deploy.sh

# 或手动部署
docker-compose up -d
```

4. **访问应用**
- 前端界面: http://localhost:3000
- 管理后台: http://localhost:3000/admin
- API 文档: http://localhost:8000/docs

### 默认管理员账号
- 用户名: `admin`
- 密码: `admin123`

**⚠️ 首次登录后请立即修改密码！**

## 📋 常用命令

### 服务管理
```bash
# 启动服务
docker-compose up -d

# 停止服务
docker-compose down

# 重启服务
docker-compose restart

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

### 数据管理
```bash
# 备份数据
./scripts/backup.sh

# 恢复数据
./scripts/restore.sh backups/linuxdo_adt_backup_YYYYMMDD_HHMMSS.tar.gz

# 查看备份列表
ls -la backups/
```

### 更新应用
```bash
# 拉取最新代码
git pull

# 重新部署
./scripts/deploy.sh
```

## 🔧 配置说明

### 重要配置项

在 `.env` 文件中需要特别注意以下配置：

```env
# 安全密钥（必须修改）
SECRET_KEY=your-very-secure-secret-key-change-this-in-production

# 数据库配置
DATABASE_URL=sqlite:///./data/linuxdo_adt.db

# CORS 配置（生产环境需要修改）
CORS_ORIGINS=http://localhost:3000,https://yourdomain.com
```

### 生产环境配置

生产环境部署时，请确保：

1. **修改 SECRET_KEY**
```bash
# 生成新的密钥
python -c "import secrets; print(secrets.token_urlsafe(32))"
```

2. **配置域名**
```env
CORS_ORIGINS=https://yourdomain.com,https://www.yourdomain.com
```

3. **使用 HTTPS**
- 配置 SSL 证书
- 使用反向代理（Nginx/Apache）

## 🐛 故障排除

### 常见问题

1. **端口被占用**
```bash
# 检查端口占用
netstat -tlnp | grep :3000
netstat -tlnp | grep :8000

# 修改端口（在 docker-compose.yml 中）
ports:
  - "3001:80"  # 前端改为 3001
  - "8001:8000"  # 后端改为 8001
```

2. **服务启动失败**
```bash
# 查看详细日志
docker-compose logs backend
docker-compose logs frontend

# 重新构建镜像
docker-compose build --no-cache
docker-compose up -d
```

3. **数据库问题**
```bash
# 检查数据库文件
ls -la data/

# 重新初始化数据库
rm data/linuxdo_adt.db
docker-compose restart backend
```

4. **权限问题**
```bash
# 修复文件权限
sudo chown -R $USER:$USER .
chmod +x scripts/*.sh
```

### 健康检查

```bash
# 检查服务状态
curl http://localhost:8000/
curl http://localhost:3000/

# 检查 API
curl http://localhost:8000/docs
```

## 📊 监控和维护

### 查看系统资源
```bash
# 查看容器资源使用
docker stats

# 查看磁盘使用
df -h
du -sh data/ logs/
```

### 日志管理
```bash
# 查看实时日志
docker-compose logs -f --tail=100

# 查看特定服务日志
docker-compose logs backend
docker-compose logs frontend

# 清理日志
docker-compose down
docker system prune -f
```

### 定期维护
```bash
# 每日备份（添加到 crontab）
0 2 * * * /path/to/linuxdo-adt/scripts/backup.sh

# 每周清理 Docker
0 3 * * 0 docker system prune -f

# 每月更新应用
0 4 1 * * cd /path/to/linuxdo-adt && git pull && ./scripts/deploy.sh
```

## 🔐 安全建议

1. **修改默认密码**
   - 登录后立即修改管理员密码
   - 使用强密码策略

2. **网络安全**
   - 使用防火墙限制访问
   - 配置 fail2ban 防止暴力破解

3. **数据安全**
   - 定期备份数据
   - 测试备份恢复流程

4. **系统更新**
   - 定期更新系统和依赖
   - 关注安全漏洞公告

## 📞 获取帮助

如果遇到问题，请：

1. 查看日志文件
2. 检查配置文件
3. 参考完整部署文档 `DEPLOYMENT.md`
4. 提交 Issue 或联系技术支持

---

**祝您使用愉快！** 🎉
