"""
邮箱验证检查装饰器
用于保护需要邮箱验证的API端点
"""

from fastapi import HTTPException, status, Depends
from sqlalchemy.ext.asyncio import AsyncSession
from .database import get_async_db
from .models import User
from .auth import get_current_active_user


async def require_email_verification(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_async_db)
) -> User:
    """
    检查用户是否已验证邮箱（管理员免检）

    Args:
        current_user: 当前用户
        db: 数据库会话

    Returns:
        User: 已验证邮箱的用户或管理员

    Raises:
        HTTPException: 如果邮箱未验证且不是管理员
    """
    # 管理员可以绕过邮箱验证检查
    if current_user.role == "admin":
        return current_user

    # 普通用户需要验证邮箱
    if not current_user.email_verified:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail={
                "message": "需要验证邮箱后才能执行此操作",
                "error_code": "EMAIL_NOT_VERIFIED",
                "user_email": current_user.email,
                "hint": "管理员账户无需邮箱验证"
            }
        )

    return current_user


async def check_email_verification_optional(
    current_user: User = Depends(get_current_active_user)
) -> dict:
    """
    可选的邮箱验证检查，返回验证状态但不阻止操作

    Args:
        current_user: 当前用户

    Returns:
        dict: 包含用户和验证状态的信息
    """
    return {
        "user": current_user,
        "email_verified": current_user.email_verified,
        "email_verified_at": current_user.email_verified_at,
        "is_admin": current_user.role == "admin"
    }


async def require_admin_or_verified_user(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_async_db)
) -> User:
    """
    检查用户是否为管理员或已验证邮箱的用户
    管理员可以绕过所有验证要求

    Args:
        current_user: 当前用户
        db: 数据库会话

    Returns:
        User: 管理员或已验证邮箱的用户

    Raises:
        HTTPException: 如果不是管理员且邮箱未验证
    """
    # 管理员拥有最高权限，可以绕过所有检查
    if current_user.role == "admin":
        return current_user

    # 普通用户需要验证邮箱
    if not current_user.email_verified:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail={
                "message": "需要验证邮箱后才能执行此操作，或联系管理员",
                "error_code": "EMAIL_NOT_VERIFIED",
                "user_email": current_user.email,
                "user_role": current_user.role,
                "hint": "管理员账户无需邮箱验证，拥有所有权限"
            }
        )

    return current_user
