version: '3.8'

services:
  # 后端服务
  backend:
    build:
      context: ./linuxdo-adt-backend
      dockerfile: Dockerfile
    container_name: linuxdo-adt-backend
    restart: unless-stopped
    ports:
      - "8000:8000"
    environment:
      - SECRET_KEY=${SECRET_KEY:-your-secret-key-here-change-in-production}
      - ALGORITHM=${ALGORITHM:-HS256}
      - ACCESS_TOKEN_EXPIRE_MINUTES=${ACCESS_TOKEN_EXPIRE_MINUTES:-30}
      - DATABASE_URL=${DATABASE_URL:-sqlite:///./data/linuxdo_adt.db}
      - CORS_ORIGINS=${CORS_ORIGINS:-http://localhost:3000,http://127.0.0.1:3000}
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    networks:
      - linuxdo-adt-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # 前端服务
  frontend:
    build:
      context: ./linuxdo-adt-frontend
      dockerfile: Dockerfile
    container_name: linuxdo-adt-frontend
    restart: unless-stopped
    ports:
      - "3000:80"
    depends_on:
      - backend
    networks:
      - linuxdo-adt-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # 数据库服务（可选，如果使用 PostgreSQL）
  # database:
  #   image: postgres:15-alpine
  #   container_name: linuxdo-adt-db
  #   restart: unless-stopped
  #   environment:
  #     POSTGRES_DB: ${POSTGRES_DB:-linuxdo_adt}
  #     POSTGRES_USER: ${POSTGRES_USER:-linuxdo_adt}
  #     POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-your-secure-password}
  #   volumes:
  #     - postgres_data:/var/lib/postgresql/data
  #   networks:
  #     - linuxdo-adt-network
  #   healthcheck:
  #     test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-linuxdo_adt}"]
  #     interval: 30s
  #     timeout: 10s
  #     retries: 3

  # Redis 缓存服务（可选）
  # redis:
  #   image: redis:7-alpine
  #   container_name: linuxdo-adt-redis
  #   restart: unless-stopped
  #   command: redis-server --appendonly yes
  #   volumes:
  #     - redis_data:/data
  #   networks:
  #     - linuxdo-adt-network
  #   healthcheck:
  #     test: ["CMD", "redis-cli", "ping"]
  #     interval: 30s
  #     timeout: 10s
  #     retries: 3

  # Nginx 反向代理（可选，用于生产环境）
  # nginx:
  #   image: nginx:alpine
  #   container_name: linuxdo-adt-nginx
  #   restart: unless-stopped
  #   ports:
  #     - "80:80"
  #     - "443:443"
  #   volumes:
  #     - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
  #     - ./nginx/conf.d:/etc/nginx/conf.d:ro
  #     - ./ssl:/etc/nginx/ssl:ro
  #   depends_on:
  #     - frontend
  #     - backend
  #   networks:
  #     - linuxdo-adt-network

networks:
  linuxdo-adt-network:
    driver: bridge

volumes:
  # postgres_data:
  # redis_data:
  data:
    driver: local
  logs:
    driver: local
