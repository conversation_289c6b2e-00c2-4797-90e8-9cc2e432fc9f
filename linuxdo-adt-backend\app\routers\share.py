from typing import List

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from ..database import get_async_db
from ..models import Task, AccountSubmission, SystemNotice, china_now
from ..schemas import SharedTaskInfo, AccountSubmissionCreate, SystemNoticeResponse

router = APIRouter(prefix="/api/share", tags=["分享页面"])


@router.get("/{share_token}", response_model=SharedTaskInfo)
async def get_shared_task(share_token: str, db: AsyncSession = Depends(get_async_db)):
    """获取分享任务信息"""
    stmt = select(Task).where(Task.share_token == share_token)
    result = await db.execute(stmt)
    task = result.scalar_one_or_none()

    if not task:
        raise HTTPException(status_code=404, detail="Task not found")

    # 检查任务是否过期
    current_time = china_now()
    if task.expires_at and task.expires_at < current_time:
        raise HTTPException(status_code=410, detail="Task has expired")

    # 检查是否已经有人提交过账号信息
    stmt = select(AccountSubmission).where(AccountSubmission.task_id == task.id)
    result = await db.execute(stmt)
    existing_submission = result.scalar_one_or_none()

    return SharedTaskInfo(
        id=task.id,
        title=task.title,
        description=task.description,
        task_type=task.task_type,
        duration_days=task.duration_days,
        created_at=task.created_at,
        is_submitted=existing_submission is not None
    )


@router.post("/{share_token}/submit")
async def submit_account_info(
    share_token: str,
    account_info: AccountSubmissionCreate,
    db: AsyncSession = Depends(get_async_db)
):
    """提交账号信息"""
    stmt = select(Task).where(Task.share_token == share_token)
    result = await db.execute(stmt)
    task = result.scalar_one_or_none()

    if not task:
        raise HTTPException(status_code=404, detail="Task not found")

    # 检查任务是否过期
    current_time = china_now()
    if task.expires_at and task.expires_at < current_time:
        raise HTTPException(status_code=410, detail="Task has expired")

    # 检查任务状态
    if task.status != "待提交":
        raise HTTPException(status_code=400, detail="Task is not available for submission")

    # 检查是否已经提交过
    stmt = select(AccountSubmission).where(AccountSubmission.task_id == task.id)
    result = await db.execute(stmt)
    existing_submission = result.scalar_one_or_none()

    if existing_submission:
        raise HTTPException(status_code=400, detail="Account information already submitted for this task")

    # 创建账号提交记录
    submission = AccountSubmission(
        task_id=task.id,
        username=account_info.username,
        password=account_info.password,
        email=account_info.email,
        level_info=account_info.level_info,
        key_info=account_info.key_info,
        need_notification=account_info.need_notification
    )

    # 更新任务状态和标题
    task.status = "进行中"
    # 更新任务标题为：用户名 + 任务类型
    task.title = f"{account_info.username}+{task.task_type}"

    db.add(submission)
    await db.commit()

    return {"message": "Account information submitted successfully"}


@router.get("/{share_token}/notices", response_model=List[SystemNoticeResponse])
async def get_notices_for_share(share_token: str, db: AsyncSession = Depends(get_async_db)):
    """获取分享页面的系统提示信息"""
    # 验证分享令牌是否有效
    stmt = select(Task).where(Task.share_token == share_token)
    result = await db.execute(stmt)
    task = result.scalar_one_or_none()
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")

    # 获取活跃的系统提示信息，按创建时间倒序排列
    stmt = select(SystemNotice).where(SystemNotice.is_active == True).order_by(SystemNotice.created_at.desc())
    result = await db.execute(stmt)
    notices = result.scalars().all()
    return notices
