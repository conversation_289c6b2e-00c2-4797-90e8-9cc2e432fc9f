#!/bin/bash

# LinuxDo ADT 部署脚本
# 用途：自动化部署应用到生产环境

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        log_error "$1 命令未找到，请先安装"
        exit 1
    fi
}

# 检查必要的命令
log_info "检查必要的命令..."
check_command "docker"
check_command "docker-compose"
check_command "git"

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

log_info "项目目录: $PROJECT_DIR"

# 切换到项目目录
cd "$PROJECT_DIR"

# 检查是否存在 .env 文件
if [ ! -f ".env" ]; then
    log_warning ".env 文件不存在，从模板创建..."
    if [ -f ".env.example" ]; then
        cp .env.example .env
        log_warning "请编辑 .env 文件配置生产环境参数"
        log_warning "特别注意修改 SECRET_KEY 和数据库配置"
    else
        log_error ".env.example 文件不存在"
        exit 1
    fi
fi

# 拉取最新代码
log_info "拉取最新代码..."
git pull origin main || {
    log_warning "Git pull 失败，继续使用当前代码"
}

# 创建必要的目录
log_info "创建必要的目录..."
mkdir -p data logs

# 停止现有服务
log_info "停止现有服务..."
docker-compose down || {
    log_warning "停止服务失败，可能服务未运行"
}

# 构建镜像
log_info "构建 Docker 镜像..."
docker-compose build --no-cache

# 启动服务
log_info "启动服务..."
docker-compose up -d

# 等待服务启动
log_info "等待服务启动..."
sleep 30

# 检查服务状态
log_info "检查服务状态..."
if docker-compose ps | grep -q "Up"; then
    log_success "服务启动成功"
else
    log_error "服务启动失败"
    docker-compose logs
    exit 1
fi

# 健康检查
log_info "执行健康检查..."
backend_health=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8000/ || echo "000")
frontend_health=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3000/ || echo "000")

if [ "$backend_health" = "200" ]; then
    log_success "后端服务健康检查通过"
else
    log_error "后端服务健康检查失败 (HTTP $backend_health)"
fi

if [ "$frontend_health" = "200" ]; then
    log_success "前端服务健康检查通过"
else
    log_error "前端服务健康检查失败 (HTTP $frontend_health)"
fi

# 显示服务信息
log_info "服务信息:"
echo "前端地址: http://localhost:3000"
echo "后端地址: http://localhost:8000"
echo "API 文档: http://localhost:8000/docs"

# 显示日志查看命令
log_info "查看日志命令:"
echo "所有服务: docker-compose logs -f"
echo "后端服务: docker-compose logs -f backend"
echo "前端服务: docker-compose logs -f frontend"

log_success "部署完成！"
