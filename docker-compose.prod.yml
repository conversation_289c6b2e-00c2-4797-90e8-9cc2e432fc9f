version: '3.8'

services:
  # 后端服务
  backend:
    build:
      context: ./linuxdo-adt-backend
      dockerfile: Dockerfile
    container_name: linuxdo-adt-backend-prod
    restart: unless-stopped
    environment:
      - SECRET_KEY=${SECRET_KEY}
      - ALGORITHM=${ALGORITHM:-HS256}
      - ACCESS_TOKEN_EXPIRE_MINUTES=${ACCESS_TOKEN_EXPIRE_MINUTES:-30}
      - DATABASE_URL=${DATABASE_URL:-sqlite:///./data/linuxdo_adt.db}
      - CORS_ORIGINS=${CORS_ORIGINS}
      - DEBUG=false
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./uploads:/app/uploads
    networks:
      - linuxdo-adt-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  # 前端服务
  frontend:
    build:
      context: ./linuxdo-adt-frontend
      dockerfile: Dockerfile
    container_name: linuxdo-adt-frontend-prod
    restart: unless-stopped
    depends_on:
      - backend
    networks:
      - linuxdo-adt-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          memory: 128M
        reservations:
          memory: 64M

  # Nginx 反向代理
  nginx:
    image: nginx:alpine
    container_name: linuxdo-adt-nginx-prod
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/conf.d/default.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
      - ./logs/nginx:/var/log/nginx
      - certbot-www:/var/www/certbot:ro
    depends_on:
      - frontend
      - backend
    networks:
      - linuxdo-adt-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 128M
        reservations:
          memory: 64M

  # Let's Encrypt SSL 证书管理
  certbot:
    image: certbot/certbot
    container_name: linuxdo-adt-certbot
    volumes:
      - ./ssl:/etc/letsencrypt
      - certbot-www:/var/www/certbot
    command: certonly --webroot --webroot-path=/var/www/certbot --email ${SSL_EMAIL} --agree-tos --no-eff-email -d ${DOMAIN} -d www.${DOMAIN}
    depends_on:
      - nginx
    profiles:
      - ssl

  # PostgreSQL 数据库（可选）
  database:
    image: postgres:15-alpine
    container_name: linuxdo-adt-db-prod
    restart: unless-stopped
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-linuxdo_adt}
      POSTGRES_USER: ${POSTGRES_USER:-linuxdo_adt}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backups/postgres:/backups
    networks:
      - linuxdo-adt-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-linuxdo_adt}"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M
    profiles:
      - postgres

  # Redis 缓存（可选）
  redis:
    image: redis:7-alpine
    container_name: linuxdo-adt-redis-prod
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    networks:
      - linuxdo-adt-network
    healthcheck:
      test: ["CMD", "redis-cli", "--no-auth-warning", "-a", "${REDIS_PASSWORD}", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 256M
        reservations:
          memory: 128M
    profiles:
      - redis

  # 监控服务（可选）
  prometheus:
    image: prom/prometheus:latest
    container_name: linuxdo-adt-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - linuxdo-adt-network
    profiles:
      - monitoring

  grafana:
    image: grafana/grafana:latest
    container_name: linuxdo-adt-grafana
    restart: unless-stopped
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - linuxdo-adt-network
    profiles:
      - monitoring

networks:
  linuxdo-adt-network:
    driver: bridge

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  certbot-www:
    driver: local
