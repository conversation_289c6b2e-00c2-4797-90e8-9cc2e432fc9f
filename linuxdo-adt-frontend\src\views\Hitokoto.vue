<template>
  <div class="hitokoto-page" :class="{ 'dark-mode': isDarkMode }">
    <div class="background-blur"></div>
    <div class="content-container">
      <div class="hitokoto-content">
        <p id="hitokoto" class="hitokoto-text">
          <a href="#" id="hitokoto_text" :class="{ 'dark-text': isDarkMode }">
            「{{ hitokotoText }}」
          </a>
        </p>
        <div class="hitokoto-meta" v-if="hitokotoData.from || hitokotoData.from_who">
          <span class="signature">
            —— {{ hitokotoData.from_who || '佚名' }}{{ hitokotoData.from ? `「${hitokotoData.from}」` : '' }}
          </span>
        </div>
      </div>
      
      <!-- 管理后台入口 -->
      <div class="admin-entrance">
        <el-button 
          type="primary" 
          size="small" 
          @click="goToAdmin"
          :class="{ 'dark-button': isDarkMode }"
        >
          管理后台
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// 一言数据
const hitokotoText = ref(':D 获取中...')
const hitokotoData = ref<any>({})

// 检测是否为夜间模式
const isDarkMode = computed(() => {
  const hour = new Date().getHours()
  return hour >= 18 || hour < 6 // 18:00-6:00 为夜间模式
})

// 获取一言
const fetchHitokoto = async () => {
  try {
    const response = await fetch('https://v1.hitokoto.cn')
    const data = await response.json()

    hitokotoText.value = data.hitokoto
    hitokotoData.value = data

    // 更新链接
    const linkElement = document.querySelector('#hitokoto_text') as HTMLAnchorElement
    if (linkElement && data.uuid) {
      linkElement.href = `https://hitokoto.cn/?uuid=${data.uuid}`
    }

    // 存储一言数据到后端
    await storeHitokoto(data)

  } catch (error) {
    console.error('获取一言失败:', error)
    // 使用默认文案
    hitokotoText.value = '行百里者，半于九十。'
    hitokotoData.value = {
      from: '《战国策·秦策五》',
      from_who: '刘向'
    }

    // 清除链接
    const linkElement = document.querySelector('#hitokoto_text') as HTMLAnchorElement
    if (linkElement) {
      linkElement.href = '#'
    }
  }
}

// 存储一言数据到后端
const storeHitokoto = async (data: any) => {
  try {
    await fetch('/api/hitokoto/store', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        hitokoto_id: data.id,
        uuid: data.uuid,
        hitokoto: data.hitokoto,
        type: data.type,
        from_text: data.from,
        from_who: data.from_who,
        creator: data.creator,
        creator_uid: data.creator_uid,
        reviewer: data.reviewer,
        commit_from: data.commit_from,
        hitokoto_created_at: data.created_at,
        length: data.length
      })
    })
  } catch (error) {
    // 存储失败不影响显示，只记录错误
    console.error('存储一言数据失败:', error)
  }
}

// 跳转到管理后台
const goToAdmin = () => {
  router.push('/admin/login')
}

onMounted(() => {
  fetchHitokoto()
})
</script>

<style scoped>
.hitokoto-page {
  position: relative;
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  transition: all 0.3s ease;
}

/* 背景模糊效果 */
.background-blur {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  filter: blur(20px);
  z-index: -1;
  transition: background 0.3s ease;
}

/* 夜间模式背景 */
.hitokoto-page.dark-mode .background-blur {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
}

.content-container {
  position: relative;
  z-index: 1;
  text-align: center;
  max-width: 800px;
  padding: 2rem;
}

.hitokoto-content {
  margin-bottom: 3rem;
}

.hitokoto-text {
  margin: 0;
  padding: 0;
}

#hitokoto_text {
  font-size: 2rem;
  line-height: 1.6;
  color: #2c3e50;
  text-decoration: none;
  font-weight: 300;
  letter-spacing: 1px;
  transition: all 0.3s ease;
  display: inline-block;
  padding: 1rem 2rem;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

#hitokoto_text:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  background: rgba(255, 255, 255, 0.15);
}

/* 夜间模式文字 */
#hitokoto_text.dark-text {
  color: #ecf0f1;
  background: rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

#hitokoto_text.dark-text:hover {
  background: rgba(0, 0, 0, 0.15);
}

.hitokoto-meta {
  margin-top: 1.5rem;
  font-size: 1rem;
  color: #7f8c8d;
  transition: color 0.3s ease;
}

.hitokoto-page.dark-mode .hitokoto-meta {
  color: #bdc3c7;
}

.signature {
  font-style: italic;
  font-weight: 300;
}

.admin-entrance {
  position: fixed;
  top: 2rem;
  right: 2rem;
  z-index: 10;
}

.admin-entrance .el-button {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: #409eff;
  transition: all 0.3s ease;
}

.admin-entrance .el-button:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

.admin-entrance .el-button.dark-button {
  background: rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: #74b9ff;
}

.admin-entrance .el-button.dark-button:hover {
  background: rgba(0, 0, 0, 0.2);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .content-container {
    padding: 1rem;
  }
  
  #hitokoto_text {
    font-size: 1.5rem;
    padding: 0.8rem 1.5rem;
  }
  
  .admin-entrance {
    top: 1rem;
    right: 1rem;
  }
  
  .hitokoto-meta {
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  #hitokoto_text {
    font-size: 1.2rem;
    padding: 0.6rem 1rem;
  }
  
  .hitokoto-meta {
    font-size: 0.8rem;
  }
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.hitokoto-content {
  animation: fadeIn 1s ease-out;
}

.admin-entrance {
  animation: fadeIn 1s ease-out 0.5s both;
}
</style>
