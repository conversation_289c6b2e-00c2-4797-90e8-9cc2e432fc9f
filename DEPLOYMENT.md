# LinuxDo ADT 部署文档

## 目录
- [部署方式概览](#部署方式概览)
- [Docker 部署（推荐）](#docker-部署推荐)
- [传统部署](#传统部署)
- [生产环境配置](#生产环境配置)
- [反向代理配置](#反向代理配置)
- [监控和维护](#监控和维护)

## 部署方式概览

LinuxDo ADT 支持多种部署方式：

1. **Docker Compose 部署**（推荐）- 一键部署，包含所有依赖
2. **Docker 单容器部署** - 灵活的容器化部署
3. **传统部署** - 直接在服务器上运行

## Docker 部署（推荐）

### 方式一：开发环境部署

1. **克隆项目**
```bash
git clone <your-repo-url>
cd linuxdo-adt
```

2. **配置环境变量**
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量
nano .env
```

3. **启动服务**
```bash
# 构建并启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

4. **访问应用**
- 前端界面: http://localhost:3000
- API 文档: http://localhost:8000/docs
- 管理后台: http://localhost:3000/admin

### 方式二：生产环境部署

1. **准备生产环境配置**
```bash
# 复制生产环境配置
cp .env.example .env

# 编辑生产环境变量
nano .env
```

生产环境 `.env` 配置示例：
```env
# 安全配置
SECRET_KEY=your-very-secure-production-secret-key
CORS_ORIGINS=https://yourdomain.com,https://www.yourdomain.com

# 域名配置
DOMAIN=yourdomain.com
SSL_EMAIL=<EMAIL>

# 数据库配置（PostgreSQL）
DATABASE_URL=******************************************************/linuxdo_adt
POSTGRES_DB=linuxdo_adt
POSTGRES_USER=linuxdo_adt
POSTGRES_PASSWORD=secure_password

# Redis 配置
REDIS_PASSWORD=secure_redis_password

# 监控配置
GRAFANA_PASSWORD=secure_grafana_password
```

2. **启动生产环境服务**
```bash
# 基础服务（前端+后端+Nginx）
docker-compose -f docker-compose.prod.yml up -d

# 包含 PostgreSQL
docker-compose -f docker-compose.prod.yml --profile postgres up -d

# 包含 Redis 缓存
docker-compose -f docker-compose.prod.yml --profile postgres --profile redis up -d

# 包含监控服务
docker-compose -f docker-compose.prod.yml --profile postgres --profile redis --profile monitoring up -d
```

3. **配置 SSL 证书**
```bash
# 首次申请证书
docker-compose -f docker-compose.prod.yml --profile ssl run --rm certbot

# 设置自动续期
echo "0 12 * * * /usr/bin/docker-compose -f /path/to/docker-compose.prod.yml --profile ssl run --rm certbot renew" | crontab -
```

4. **访问生产环境**
- 前端界面: https://yourdomain.com
- 管理后台: https://yourdomain.com/admin
- API 文档: https://yourdomain.com/docs
- 监控面板: https://yourdomain.com:3001 (如果启用)

### 方式二：单独构建 Docker 镜像

1. **构建后端镜像**
```bash
cd linuxdo-adt-backend
docker build -t linuxdo-adt-backend .
```

2. **构建前端镜像**
```bash
cd linuxdo-adt-frontend
docker build -t linuxdo-adt-frontend .
```

3. **运行容器**
```bash
# 创建网络
docker network create linuxdo-adt-network

# 运行后端
docker run -d \
  --name linuxdo-adt-backend \
  --network linuxdo-adt-network \
  -p 8000:8000 \
  -v $(pwd)/data:/app/data \
  -e SECRET_KEY="your-production-secret-key" \
  -e DATABASE_URL="sqlite:///./data/linuxdo_adt.db" \
  linuxdo-adt-backend

# 运行前端
docker run -d \
  --name linuxdo-adt-frontend \
  --network linuxdo-adt-network \
  -p 3000:80 \
  linuxdo-adt-frontend
```

## 传统部署

### 后端部署

1. **环境准备**
```bash
# 安装 Python 3.8+
python3 --version

# 创建虚拟环境
python3 -m venv venv
source venv/bin/activate  # Linux/Mac
# 或 venv\Scripts\activate  # Windows
```

2. **安装依赖**
```bash
cd linuxdo-adt-backend
pip install -r requirements.txt
```

3. **配置环境变量**
```bash
# 复制并编辑环境变量
cp .env.example .env
nano .env
```

4. **初始化数据库**
```bash
# 运行数据库迁移（如果有）
python -c "from app.database import engine, Base; Base.metadata.create_all(bind=engine)"
```

5. **启动后端服务**
```bash
# 开发环境
python run.py

# 生产环境
uvicorn app.main:app --host 0.0.0.0 --port 8000 --workers 4
```

### 前端部署

1. **环境准备**
```bash
# 安装 Node.js 16+
node --version
npm --version
```

2. **安装依赖**
```bash
cd linuxdo-adt-frontend
npm install
```

3. **构建生产版本**
```bash
# 构建
npm run build

# 构建产物在 dist/ 目录
```

4. **部署静态文件**
```bash
# 方式一：使用 nginx 托管
sudo cp -r dist/* /var/www/html/

# 方式二：使用 Node.js 服务器
npm install -g serve
serve -s dist -l 3000
```

## 生产环境配置

### 环境变量配置

创建 `.env` 文件：

```env
# 安全配置
SECRET_KEY=your-very-secure-secret-key-change-this-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# 数据库配置
DATABASE_URL=sqlite:///./data/linuxdo_adt.db
# 或使用 PostgreSQL
# DATABASE_URL=postgresql://user:password@localhost/linuxdo_adt

# 邮件配置（可选）
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_USE_TLS=true

# 应用配置
CORS_ORIGINS=https://yourdomain.com,https://www.yourdomain.com
DEBUG=false
```

### 安全配置

1. **更改默认密钥**
```bash
# 生成新的 SECRET_KEY
python -c "import secrets; print(secrets.token_urlsafe(32))"
```

2. **配置 HTTPS**
- 使用 Let's Encrypt 获取 SSL 证书
- 配置反向代理强制 HTTPS

3. **数据库安全**
- 使用强密码
- 限制数据库访问权限
- 定期备份数据

## 反向代理配置

### Nginx 配置示例

创建 `/etc/nginx/sites-available/linuxdo-adt`：

```nginx
server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com www.yourdomain.com;

    # SSL 配置
    ssl_certificate /path/to/your/cert.pem;
    ssl_certificate_key /path/to/your/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;

    # 前端静态文件
    location / {
        root /var/www/linuxdo-adt;
        try_files $uri $uri/ /index.html;
        
        # 缓存静态资源
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }

    # API 代理
    location /api/ {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket 支持（如果需要）
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }

    # 安全头
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
}
```

启用配置：
```bash
sudo ln -s /etc/nginx/sites-available/linuxdo-adt /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### Apache 配置示例

创建虚拟主机配置：

```apache
<VirtualHost *:80>
    ServerName yourdomain.com
    Redirect permanent / https://yourdomain.com/
</VirtualHost>

<VirtualHost *:443>
    ServerName yourdomain.com
    DocumentRoot /var/www/linuxdo-adt

    # SSL 配置
    SSLEngine on
    SSLCertificateFile /path/to/your/cert.pem
    SSLCertificateKeyFile /path/to/your/private.key

    # 前端静态文件
    <Directory /var/www/linuxdo-adt>
        AllowOverride All
        Require all granted
        
        # SPA 路由支持
        RewriteEngine On
        RewriteBase /
        RewriteRule ^index\.html$ - [L]
        RewriteCond %{REQUEST_FILENAME} !-f
        RewriteCond %{REQUEST_FILENAME} !-d
        RewriteRule . /index.html [L]
    </Directory>

    # API 代理
    ProxyPreserveHost On
    ProxyPass /api/ http://127.0.0.1:8000/api/
    ProxyPassReverse /api/ http://127.0.0.1:8000/api/
</VirtualHost>
```

## 监控和维护

### 系统服务配置

创建 systemd 服务文件 `/etc/systemd/system/linuxdo-adt-backend.service`：

```ini
[Unit]
Description=LinuxDo ADT Backend
After=network.target

[Service]
Type=exec
User=www-data
Group=www-data
WorkingDirectory=/opt/linuxdo-adt/linuxdo-adt-backend
Environment=PATH=/opt/linuxdo-adt/venv/bin
ExecStart=/opt/linuxdo-adt/venv/bin/uvicorn app.main:app --host 0.0.0.0 --port 8000 --workers 4
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
```

启用服务：
```bash
sudo systemctl daemon-reload
sudo systemctl enable linuxdo-adt-backend
sudo systemctl start linuxdo-adt-backend
sudo systemctl status linuxdo-adt-backend
```

### 日志管理

1. **应用日志**
```bash
# 查看后端日志
sudo journalctl -u linuxdo-adt-backend -f

# 查看 Nginx 日志
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log
```

2. **日志轮转**
创建 `/etc/logrotate.d/linuxdo-adt`：
```
/var/log/linuxdo-adt/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
    postrotate
        systemctl reload linuxdo-adt-backend
    endscript
}
```

### 备份策略

1. **数据库备份**
```bash
#!/bin/bash
# backup.sh
BACKUP_DIR="/opt/backups/linuxdo-adt"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p $BACKUP_DIR

# 备份 SQLite 数据库
cp /opt/linuxdo-adt/linuxdo-adt-backend/linuxdo_adt.db $BACKUP_DIR/linuxdo_adt_$DATE.db

# 压缩备份
gzip $BACKUP_DIR/linuxdo_adt_$DATE.db

# 删除 30 天前的备份
find $BACKUP_DIR -name "*.gz" -mtime +30 -delete
```

2. **定时备份**
```bash
# 添加到 crontab
crontab -e

# 每天凌晨 2 点备份
0 2 * * * /opt/scripts/backup.sh
```

### 性能监控

1. **系统监控**
```bash
# 安装监控工具
sudo apt install htop iotop nethogs

# 监控系统资源
htop
iotop
nethogs
```

2. **应用监控**
```bash
# 监控应用进程
ps aux | grep uvicorn
ss -tlnp | grep :8000

# 检查应用健康状态
curl http://localhost:8000/
curl http://localhost:8000/api/
```

### 故障排除

1. **常见问题**
- 端口被占用：`sudo lsof -i :8000`
- 权限问题：检查文件和目录权限
- 数据库连接：检查数据库文件路径和权限

2. **调试模式**
```bash
# 启用调试模式
export DEBUG=true
python run.py
```

3. **健康检查**
```bash
# 检查服务状态
curl -f http://localhost:8000/ || echo "Backend is down"
curl -f http://localhost:3000/ || echo "Frontend is down"
```

## 更新部署

### Docker 更新
```bash
# 拉取最新代码
git pull

# 重新构建并启动
docker-compose down
docker-compose build --no-cache
docker-compose up -d
```

### 传统部署更新
```bash
# 拉取最新代码
git pull

# 更新后端
cd linuxdo-adt-backend
pip install -r requirements.txt
sudo systemctl restart linuxdo-adt-backend

# 更新前端
cd ../linuxdo-adt-frontend
npm install
npm run build
sudo cp -r dist/* /var/www/linuxdo-adt/
```

## 安全建议

1. **定期更新**
   - 定期更新系统和依赖包
   - 关注安全漏洞公告

2. **访问控制**
   - 使用防火墙限制访问
   - 配置 fail2ban 防止暴力破解

3. **监控告警**
   - 设置系统监控告警
   - 监控异常访问日志

4. **数据保护**
   - 定期备份重要数据
   - 测试备份恢复流程
