#!/bin/bash

# LinuxDo ADT 备份脚本
# 用途：备份数据库和重要文件

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
BACKUP_DIR="${PROJECT_DIR}/backups"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_NAME="linuxdo_adt_backup_${DATE}"

# 创建备份目录
mkdir -p "$BACKUP_DIR"

log_info "开始备份..."
log_info "备份目录: $BACKUP_DIR"
log_info "备份名称: $BACKUP_NAME"

# 创建临时备份目录
TEMP_BACKUP_DIR="${BACKUP_DIR}/${BACKUP_NAME}"
mkdir -p "$TEMP_BACKUP_DIR"

# 备份数据库
log_info "备份数据库..."
if [ -f "${PROJECT_DIR}/data/linuxdo_adt.db" ]; then
    cp "${PROJECT_DIR}/data/linuxdo_adt.db" "${TEMP_BACKUP_DIR}/linuxdo_adt.db"
    log_success "数据库备份完成"
else
    log_warning "数据库文件不存在，跳过数据库备份"
fi

# 备份配置文件
log_info "备份配置文件..."
if [ -f "${PROJECT_DIR}/.env" ]; then
    cp "${PROJECT_DIR}/.env" "${TEMP_BACKUP_DIR}/.env"
    log_success "环境配置备份完成"
fi

if [ -f "${PROJECT_DIR}/docker-compose.yml" ]; then
    cp "${PROJECT_DIR}/docker-compose.yml" "${TEMP_BACKUP_DIR}/docker-compose.yml"
    log_success "Docker Compose 配置备份完成"
fi

# 备份日志文件
log_info "备份日志文件..."
if [ -d "${PROJECT_DIR}/logs" ]; then
    cp -r "${PROJECT_DIR}/logs" "${TEMP_BACKUP_DIR}/"
    log_success "日志文件备份完成"
else
    log_warning "日志目录不存在，跳过日志备份"
fi

# 备份上传文件（如果存在）
if [ -d "${PROJECT_DIR}/uploads" ]; then
    log_info "备份上传文件..."
    cp -r "${PROJECT_DIR}/uploads" "${TEMP_BACKUP_DIR}/"
    log_success "上传文件备份完成"
fi

# 创建备份信息文件
log_info "创建备份信息文件..."
cat > "${TEMP_BACKUP_DIR}/backup_info.txt" << EOF
LinuxDo ADT 备份信息
==================

备份时间: $(date)
备份版本: ${DATE}
Git 提交: $(git rev-parse HEAD 2>/dev/null || echo "未知")
Git 分支: $(git branch --show-current 2>/dev/null || echo "未知")

备份内容:
- 数据库文件
- 环境配置文件
- Docker Compose 配置
- 日志文件
- 上传文件（如果存在）

恢复说明:
1. 停止当前服务: docker-compose down
2. 恢复数据库: cp linuxdo_adt.db ../data/
3. 恢复配置: cp .env ../
4. 启动服务: docker-compose up -d
EOF

# 压缩备份
log_info "压缩备份文件..."
cd "$BACKUP_DIR"
tar -czf "${BACKUP_NAME}.tar.gz" "$BACKUP_NAME"

# 删除临时目录
rm -rf "$TEMP_BACKUP_DIR"

# 计算备份文件大小
BACKUP_SIZE=$(du -h "${BACKUP_NAME}.tar.gz" | cut -f1)
log_success "备份完成: ${BACKUP_NAME}.tar.gz (${BACKUP_SIZE})"

# 清理旧备份（保留最近30个）
log_info "清理旧备份文件..."
cd "$BACKUP_DIR"
ls -t linuxdo_adt_backup_*.tar.gz | tail -n +31 | xargs -r rm -f
REMAINING_BACKUPS=$(ls -1 linuxdo_adt_backup_*.tar.gz 2>/dev/null | wc -l)
log_info "保留 ${REMAINING_BACKUPS} 个备份文件"

log_success "备份任务完成！"
echo "备份文件位置: ${BACKUP_DIR}/${BACKUP_NAME}.tar.gz"
