#!/bin/bash

# LinuxDo ADT 恢复脚本
# 用途：从备份文件恢复数据

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示使用说明
show_usage() {
    echo "使用方法: $0 <备份文件路径>"
    echo "示例: $0 backups/linuxdo_adt_backup_20240127_143022.tar.gz"
    exit 1
}

# 检查参数
if [ $# -eq 0 ]; then
    log_error "请指定备份文件路径"
    show_usage
fi

BACKUP_FILE="$1"

# 检查备份文件是否存在
if [ ! -f "$BACKUP_FILE" ]; then
    log_error "备份文件不存在: $BACKUP_FILE"
    exit 1
fi

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

log_info "项目目录: $PROJECT_DIR"
log_info "备份文件: $BACKUP_FILE"

# 确认恢复操作
echo
log_warning "警告: 此操作将覆盖当前数据！"
read -p "确定要从备份恢复吗？(y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    log_info "恢复操作已取消"
    exit 0
fi

# 切换到项目目录
cd "$PROJECT_DIR"

# 停止服务
log_info "停止当前服务..."
docker-compose down || {
    log_warning "停止服务失败，可能服务未运行"
}

# 创建恢复临时目录
TEMP_DIR=$(mktemp -d)
log_info "临时目录: $TEMP_DIR"

# 解压备份文件
log_info "解压备份文件..."
tar -xzf "$BACKUP_FILE" -C "$TEMP_DIR"

# 查找解压后的目录
BACKUP_DIR=$(find "$TEMP_DIR" -maxdepth 1 -type d -name "linuxdo_adt_backup_*" | head -n 1)

if [ -z "$BACKUP_DIR" ]; then
    log_error "备份文件格式不正确"
    rm -rf "$TEMP_DIR"
    exit 1
fi

log_info "备份目录: $BACKUP_DIR"

# 显示备份信息
if [ -f "$BACKUP_DIR/backup_info.txt" ]; then
    log_info "备份信息:"
    cat "$BACKUP_DIR/backup_info.txt"
    echo
fi

# 备份当前数据（以防恢复失败）
CURRENT_BACKUP_DIR="./backups/before_restore_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$CURRENT_BACKUP_DIR"

if [ -f "./data/linuxdo_adt.db" ]; then
    log_info "备份当前数据库..."
    cp "./data/linuxdo_adt.db" "$CURRENT_BACKUP_DIR/"
fi

if [ -f "./.env" ]; then
    log_info "备份当前配置..."
    cp "./.env" "$CURRENT_BACKUP_DIR/"
fi

# 恢复数据库
if [ -f "$BACKUP_DIR/linuxdo_adt.db" ]; then
    log_info "恢复数据库..."
    mkdir -p "./data"
    cp "$BACKUP_DIR/linuxdo_adt.db" "./data/"
    log_success "数据库恢复完成"
else
    log_warning "备份中没有数据库文件"
fi

# 恢复配置文件
if [ -f "$BACKUP_DIR/.env" ]; then
    log_info "恢复环境配置..."
    cp "$BACKUP_DIR/.env" "./"
    log_success "环境配置恢复完成"
else
    log_warning "备份中没有环境配置文件"
fi

# 恢复 Docker Compose 配置
if [ -f "$BACKUP_DIR/docker-compose.yml" ]; then
    log_info "恢复 Docker Compose 配置..."
    cp "$BACKUP_DIR/docker-compose.yml" "./"
    log_success "Docker Compose 配置恢复完成"
else
    log_warning "备份中没有 Docker Compose 配置文件"
fi

# 恢复日志文件
if [ -d "$BACKUP_DIR/logs" ]; then
    log_info "恢复日志文件..."
    cp -r "$BACKUP_DIR/logs" "./"
    log_success "日志文件恢复完成"
else
    log_warning "备份中没有日志文件"
fi

# 恢复上传文件
if [ -d "$BACKUP_DIR/uploads" ]; then
    log_info "恢复上传文件..."
    cp -r "$BACKUP_DIR/uploads" "./"
    log_success "上传文件恢复完成"
else
    log_warning "备份中没有上传文件"
fi

# 清理临时目录
rm -rf "$TEMP_DIR"

# 启动服务
log_info "启动服务..."
docker-compose up -d

# 等待服务启动
log_info "等待服务启动..."
sleep 30

# 健康检查
log_info "执行健康检查..."
backend_health=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8000/ || echo "000")
frontend_health=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3000/ || echo "000")

if [ "$backend_health" = "200" ]; then
    log_success "后端服务健康检查通过"
else
    log_error "后端服务健康检查失败 (HTTP $backend_health)"
fi

if [ "$frontend_health" = "200" ]; then
    log_success "前端服务健康检查通过"
else
    log_error "前端服务健康检查失败 (HTTP $frontend_health)"
fi

log_success "恢复完成！"
log_info "当前数据已备份到: $CURRENT_BACKUP_DIR"
echo "前端地址: http://localhost:3000"
echo "后端地址: http://localhost:8000"
echo "API 文档: http://localhost:8000/docs"
