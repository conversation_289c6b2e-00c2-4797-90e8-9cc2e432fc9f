from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import func, select
from typing import List, Optional
from ..database import get_async_db
from ..models import Hitoko<PERSON>, User
from ..schemas import HitokotoCreate, HitokotoResponse
from ..auth import get_current_user

router = APIRouter(prefix="/api/hitokoto", tags=["一言"])


async def check_admin_permission(current_user: User = Depends(get_current_user)):
    """检查管理员权限"""
    if current_user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要管理员权限"
        )
    return current_user


@router.post("/store", response_model=HitokotoResponse)
async def store_hitokoto(
    hitokoto_data: HitokotoCreate,
    db: AsyncSession = Depends(get_async_db)
):
    """存储一言数据（公开接口）"""
    try:
        # 检查是否已存在相同uuid的一言
        stmt = select(Hitokoto).where(Hitokoto.uuid == hitokoto_data.uuid)
        result = await db.execute(stmt)
        existing = result.scalar_one_or_none()
        if existing:
            return existing

        # 创建新的一言记录
        db_hitokoto = Hitokoto(
            hitokoto_id=hitokoto_data.hitokoto_id,
            uuid=hitokoto_data.uuid,
            hitokoto=hitokoto_data.hitokoto,
            type=hitokoto_data.type,
            from_text=hitokoto_data.from_text,
            from_who=hitokoto_data.from_who,
            creator=hitokoto_data.creator,
            creator_uid=hitokoto_data.creator_uid,
            reviewer=hitokoto_data.reviewer,
            commit_from=hitokoto_data.commit_from,
            hitokoto_created_at=hitokoto_data.hitokoto_created_at,
            length=hitokoto_data.length
        )

        db.add(db_hitokoto)
        await db.commit()
        await db.refresh(db_hitokoto)
        return db_hitokoto

    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"存储一言数据失败: {str(e)}"
        )


@router.get("/list", response_model=List[HitokotoResponse])
async def get_hitokoto_list(
    skip: int = 0,
    limit: int = 100,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(check_admin_permission)
):
    """获取一言列表（管理员接口）"""
    stmt = select(Hitokoto).order_by(Hitokoto.created_at.desc()).offset(skip).limit(limit)
    result = await db.execute(stmt)
    hitokoto_list = result.scalars().all()
    return hitokoto_list


@router.get("/stats")
async def get_hitokoto_stats(
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(check_admin_permission)
):
    """获取一言统计信息（管理员接口）"""
    # 总数统计
    total_stmt = select(func.count(Hitokoto.id))
    total_result = await db.execute(total_stmt)
    total_count = total_result.scalar()

    # 按类型统计
    type_stmt = select(Hitokoto.type, func.count(Hitokoto.id)).group_by(Hitokoto.type)
    type_result = await db.execute(type_stmt)
    type_stats = type_result.all()
    type_distribution = {type_name or "未知": count for type_name, count in type_stats}

    return {
        "total_count": total_count,
        "type_distribution": type_distribution
    }


@router.delete("/{hitokoto_id}")
async def delete_hitokoto(
    hitokoto_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(check_admin_permission)
):
    """删除一言记录（管理员接口）"""
    stmt = select(Hitokoto).where(Hitokoto.id == hitokoto_id)
    result = await db.execute(stmt)
    hitokoto = result.scalar_one_or_none()
    if not hitokoto:
        raise HTTPException(status_code=404, detail="一言记录不存在")

    await db.delete(hitokoto)
    await db.commit()
    return {"message": "一言记录已删除"}


@router.get("/random", response_model=HitokotoResponse)
async def get_random_hitokoto(
    db: AsyncSession = Depends(get_async_db)
):
    """获取随机一言（公开接口）"""
    stmt = select(Hitokoto).order_by(func.random())
    result = await db.execute(stmt)
    hitokoto = result.scalar_one_or_none()
    if not hitokoto:
        raise HTTPException(status_code=404, detail="暂无一言数据")
    return hitokoto
