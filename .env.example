# ===========================================
# LinuxDo ADT 环境变量配置文件
# ===========================================

# 安全配置
SECRET_KEY=your-very-secure-secret-key-change-this-in-production-use-at-least-32-characters
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# 数据库配置
# SQLite (默认)
DATABASE_URL=sqlite:///./data/linuxdo_adt.db

# PostgreSQL (可选)
# DATABASE_URL=***********************************************************/linuxdo_adt
# POSTGRES_DB=linuxdo_adt
# POSTGRES_USER=linuxdo_adt
# POSTGRES_PASSWORD=your-secure-password

# CORS 配置
CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000,https://yourdomain.com

# 邮件配置（可选）
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_USE_TLS=true
SMTP_FROM_EMAIL=<EMAIL>
SMTP_FROM_NAME=LinuxDo ADT

# 应用配置
DEBUG=false
LOG_LEVEL=INFO

# Redis 配置（可选）
# REDIS_URL=redis://redis:6379/0

# 文件上传配置
MAX_UPLOAD_SIZE=10485760  # 10MB
UPLOAD_DIR=./uploads

# 任务调度配置
SCHEDULER_ENABLED=true
SCHEDULER_TIMEZONE=Asia/Shanghai

# 安全配置
ENABLE_RATE_LIMITING=true
RATE_LIMIT_PER_MINUTE=60

# 监控配置
ENABLE_METRICS=false
METRICS_PORT=9090
