from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from ..database import get_async_db
from ..models import User
from ..schemas import (
    UserResponse,
    ChangePasswordRequest,
    SendVerificationEmailRequest,
    ProfileUpdateRequest
)
from ..auth import get_current_active_user, verify_password, get_password_hash
from ..email_verification import email_verification_service

router = APIRouter(prefix="/api/profile", tags=["个人中心"])


@router.get("/", response_model=UserResponse)
async def get_profile(current_user: User = Depends(get_current_active_user)):
    """获取个人资料"""
    return current_user


@router.put("/", response_model=UserResponse)
async def update_profile(
    profile_update: ProfileUpdateRequest,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_async_db)
):
    """更新个人资料"""
    # 检查用户名是否已被其他用户使用
    if profile_update.username and profile_update.username != current_user.username:
        stmt = select(User).where(
            User.username == profile_update.username,
            User.id != current_user.id
        )
        result = await db.execute(stmt)
        existing_user = result.scalar_one_or_none()
        if existing_user:
            raise HTTPException(status_code=400, detail="用户名已存在")

    # 检查邮箱是否已被其他用户使用
    if profile_update.email and profile_update.email != current_user.email:
        stmt = select(User).where(
            User.email == profile_update.email,
            User.id != current_user.id
        )
        result = await db.execute(stmt)
        existing_email = result.scalar_one_or_none()
        if existing_email:
            raise HTTPException(status_code=400, detail="邮箱已存在")

    # 更新用户信息
    update_data = profile_update.model_dump(exclude_unset=True)
    email_changed = False

    for field, value in update_data.items():
        if field == "email" and value != current_user.email:
            email_changed = True
            # 如果邮箱发生变化，重置邮箱验证状态
            current_user.email_verified = False
            current_user.email_verified_at = None
        setattr(current_user, field, value)

    await db.commit()
    await db.refresh(current_user)

    # 如果邮箱发生变化，自动发送验证邮件
    if email_changed:
        try:
            await email_verification_service.send_verification_email(
                user=current_user,
                email_to_verify=current_user.email,
                verification_url_base="http://localhost:5173/verify-email",
                db=db
            )
        except Exception as e:
            # 邮件发送失败不影响更新流程
            print(f"发送邮箱验证邮件失败: {str(e)}")

    return current_user


@router.post("/change-password")
async def change_password(
    password_request: ChangePasswordRequest,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_async_db)
):
    """修改密码"""
    # 验证当前密码
    if not verify_password(password_request.current_password, current_user.password_hash):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="当前密码不正确"
        )

    # 检查新密码长度
    if len(password_request.new_password) < 6:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="新密码长度至少6个字符"
        )

    # 检查新密码是否与当前密码相同
    if verify_password(password_request.new_password, current_user.password_hash):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="新密码不能与当前密码相同"
        )

    # 更新密码
    current_user.password_hash = get_password_hash(password_request.new_password)
    await db.commit()

    return {"message": "密码修改成功"}


@router.post("/send-verification-email")
async def send_verification_email(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_async_db)
):
    """发送邮箱验证邮件"""
    # 检查邮箱是否已经验证
    if current_user.email_verified:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="邮箱已经验证过了"
        )

    # 发送验证邮件
    try:
        success = await email_verification_service.send_verification_email(
            user=current_user,
            email_to_verify=current_user.email,
            verification_url_base="http://localhost:5173/verify-email",
            db=db
        )

        if success:
            return {"message": "验证邮件发送成功，请检查您的邮箱"}
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="验证邮件发送失败，请稍后重试"
            )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"发送验证邮件时发生错误: {str(e)}"
        )

@router.get("/verification-status")
async def get_verification_status(current_user: User = Depends(get_current_active_user)):
    """获取邮箱验证状态"""
    return {
        "email": current_user.email,
        "email_verified": current_user.email_verified,
        "email_verified_at": current_user.email_verified_at,
        "is_admin": current_user.role == "admin"
    }
